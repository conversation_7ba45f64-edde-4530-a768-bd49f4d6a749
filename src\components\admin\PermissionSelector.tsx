import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Shield, Eye, Edit, Trash2 } from 'lucide-react';
import type { TeamMember, PermissionLevel } from '../../types/dashboard';

interface PermissionSelectorProps {
  member: TeamMember;
  onUpdate: (memberId: string, newPermission: PermissionLevel) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export const PermissionSelector: React.FC<PermissionSelectorProps> = ({
  member,
  onUpdate,
  onCancel,
  isLoading,
}) => {
  const [selectedPermission, setSelectedPermission] = useState<PermissionLevel>(member.permission_level);

  const handleUpdate = () => {
    onUpdate(member.id, selectedPermission);
  };

  const getPermissionIcon = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return <Eye className="h-4 w-4" />;
      case 'EDIT':
        return <Edit className="h-4 w-4" />;
      case 'DELETE':
        return <Trash2 className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  const getPermissionDescription = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return 'Can view QR codes and analytics but cannot make changes';
      case 'EDIT':
        return 'Can view and modify QR codes and their settings';
      case 'DELETE':
        return 'Full access including ability to delete QR codes';
      default:
        return '';
    }
  };

  const getPermissionDetails = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return {
          title: 'View Only',
          description: 'Read-only access to QR codes and analytics',
          capabilities: [
            'View QR code list and details',
            'Access analytics and reports',
            'Download QR codes',
            'Cannot create, edit, or delete'
          ]
        };
      case 'EDIT':
        return {
          title: 'Edit Access',
          description: 'Can modify QR codes and settings',
          capabilities: [
            'All View permissions',
            'Create new QR codes',
            'Edit existing QR codes',
            'Update QR code settings',
            'Cannot delete QR codes'
          ]
        };
      case 'DELETE':
        return {
          title: 'Full Access',
          description: 'Complete control over QR codes',
          capabilities: [
            'All Edit permissions',
            'Delete QR codes',
            'Manage QR code lifecycle',
            'Full administrative access'
          ]
        };
      default:
        return {
          title: 'Unknown',
          description: '',
          capabilities: []
        };
    }
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Edit Permissions</span>
          </DialogTitle>
          <DialogDescription>
            Update permissions for <strong>{member.name || member.email}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Permission Level</Label>
            <Select 
              value={selectedPermission} 
              onValueChange={(value: PermissionLevel) => setSelectedPermission(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select permission level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="VIEW">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">View Only</span>
                      <span className="text-sm text-muted-foreground">Read-only access</span>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="EDIT">
                  <div className="flex items-center space-x-2">
                    <Edit className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">Edit Access</span>
                      <span className="text-sm text-muted-foreground">Can modify QR codes</span>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="DELETE">
                  <div className="flex items-center space-x-2">
                    <Trash2 className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">Full Access</span>
                      <span className="text-sm text-muted-foreground">Complete control</span>
                    </div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Permission Details */}
          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              {getPermissionIcon(selectedPermission)}
              <h4 className="font-medium">{getPermissionDetails(selectedPermission).title}</h4>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {getPermissionDetails(selectedPermission).description}
            </p>
            <div className="space-y-1">
              <p className="text-sm font-medium">Capabilities:</p>
              <ul className="text-sm text-muted-foreground space-y-1">
                {getPermissionDetails(selectedPermission).capabilities.map((capability, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-green-500 mt-0.5">•</span>
                    <span>{capability}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleUpdate} 
            disabled={isLoading || selectedPermission === member.permission_level}
          >
            {isLoading ? 'Updating...' : 'Update Permissions'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
