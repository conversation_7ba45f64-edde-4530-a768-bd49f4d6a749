import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Users, 
  UserPlus, 
  Search, 
  RefreshCw,
  Share2,
  Eye,
  ArrowLeftRight
} from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { ShareDashboardForm } from './ShareDashboardForm';
import { MySharesTable } from './MySharesTable';
import { SharedWithMeTable } from './SharedWithMeTable';
import { PendingShareInvitationsTable } from './PendingShareInvitationsTable';
import type { 
  DashboardShare, 
  ShareInvitation,
  SharedDashboard,
  DashboardSharesResponse, 
  ShareInvitationsResponse,
  SharedDashboardsResponse
} from '../../types/dashboard';

export const DashboardSharingPage: React.FC = () => {
  const [myShares, setMyShares] = useState<DashboardShare[]>([]);
  const [sharedWithMe, setSharedWithMe] = useState<SharedDashboard[]>([]);
  const [invitations, setInvitations] = useState<ShareInvitation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingShares, setIsLoadingShares] = useState(true);
  const [isLoadingSharedWithMe, setIsLoadingSharedWithMe] = useState(true);
  const [isLoadingInvitations, setIsLoadingInvitations] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchMyShares = async () => {
    try {
      const response = await fetch(`/api/sharing/my-shares?search=${encodeURIComponent(searchQuery)}`);
      const data: DashboardSharesResponse = await response.json();

      if (data.success && data.data) {
        setMyShares(data.data.shares);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch dashboard shares",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard shares:', error);
      toast({
        title: "Error",
        description: "Failed to fetch dashboard shares",
        variant: "destructive",
      });
    } finally {
      setIsLoadingShares(false);
    }
  };

  const fetchSharedWithMe = async () => {
    try {
      const response = await fetch('/api/sharing/shared-with-me');
      const data: SharedDashboardsResponse = await response.json();

      if (data.success && data.data) {
        setSharedWithMe(data.data.dashboards);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch shared dashboards",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching shared dashboards:', error);
      toast({
        title: "Error",
        description: "Failed to fetch shared dashboards",
        variant: "destructive",
      });
    } finally {
      setIsLoadingSharedWithMe(false);
    }
  };

  const fetchInvitations = async () => {
    try {
      const response = await fetch('/api/sharing/invitations');
      const data: ShareInvitationsResponse = await response.json();

      if (data.success && data.data) {
        setInvitations(data.data.invitations);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch invitations",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch invitations",
        variant: "destructive",
      });
    } finally {
      setIsLoadingInvitations(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([fetchMyShares(), fetchSharedWithMe(), fetchInvitations()]);
    setIsRefreshing(false);
  };

  const handleShareSent = () => {
    fetchInvitations();
  };

  const handleShareUpdated = () => {
    fetchMyShares();
  };

  const handleShareRevoked = () => {
    fetchMyShares();
  };

  const handleInvitationResent = () => {
    fetchInvitations();
  };

  useEffect(() => {
    fetchMyShares();
    fetchSharedWithMe();
    fetchInvitations();
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (!isLoadingShares) {
        fetchMyShares();
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const filteredShares = myShares.filter(share =>
    share.shared_with_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    share.shared_with_email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard Sharing</h1>
          <p className="text-muted-foreground">
            Share your QR Analytics dashboard with others and access shared dashboards
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">People I've Shared With</CardTitle>
            <Share2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{myShares.length}</div>
            <p className="text-xs text-muted-foreground">
              Active dashboard shares
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dashboards Shared With Me</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sharedWithMe.length}</div>
            <p className="text-xs text-muted-foreground">
              Accessible dashboards
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invitations</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invitations.length}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting acceptance
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Share Form */}
        <div className="lg:col-span-1">
          <ShareDashboardForm onShareSent={handleShareSent} />
        </div>

        {/* Shares and Invitations */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="my-shares" className="space-y-4">
            <TabsList>
              <TabsTrigger value="my-shares" className="flex items-center space-x-2">
                <Share2 className="h-4 w-4" />
                <span>My Shares ({myShares.length})</span>
              </TabsTrigger>
              <TabsTrigger value="shared-with-me" className="flex items-center space-x-2">
                <ArrowLeftRight className="h-4 w-4" />
                <span>Shared With Me ({sharedWithMe.length})</span>
              </TabsTrigger>
              <TabsTrigger value="invitations" className="flex items-center space-x-2">
                <UserPlus className="h-4 w-4" />
                <span>Pending Invitations ({invitations.length})</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="my-shares" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>People I've Shared My Dashboard With</CardTitle>
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                          placeholder="Search people..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10 w-64"
                        />
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoadingShares ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <MySharesTable
                      shares={filteredShares}
                      onShareUpdated={handleShareUpdated}
                      onShareRevoked={handleShareRevoked}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="shared-with-me" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Dashboards Shared With Me</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingSharedWithMe ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <SharedWithMeTable dashboards={sharedWithMe} />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="invitations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pending Invitations</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingInvitations ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <PendingShareInvitationsTable
                      invitations={invitations}
                      onInvitationResent={handleInvitationResent}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
