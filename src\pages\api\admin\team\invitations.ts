import type { APIRoute } from 'astro';
import { requireAdmin, getUserIdFromRequest } from '../../../../lib/auth-utils';
import { getDatabase } from '../../../../lib/database';
import type { InvitationsResponse } from '../../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Check admin privileges
    const adminCheck = await requireAdmin(request, db);
    if (!adminCheck.success) {
      const response: InvitationsResponse = {
        success: false,
        error: adminCheck.error || 'Admin privileges required'
      };
      return new Response(JSON.stringify(response), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get admin user ID
    const adminId = getUserIdFromRequest(request);
    if (!adminId) {
      const response: InvitationsResponse = {
        success: false,
        error: 'Admin user not found'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get query parameters for pagination and filtering
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status') || 'pending';
    const offset = (page - 1) * limit;

    // Get invitations
    const invitationsQuery = `
      SELECT 
        id,
        admin_id,
        email,
        permission_level,
        token,
        status,
        expires_at,
        created_at,
        updated_at
      FROM invitations
      WHERE admin_id = ? AND status = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const invitations = await db.prepare(invitationsQuery)
      .bind(adminId, status, limit, offset)
      .all();

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM invitations
      WHERE admin_id = ? AND status = ?
    `;

    const countResult = await db.prepare(countQuery)
      .bind(adminId, status)
      .first();

    const total = countResult?.total || 0;

    // Update expired invitations
    await db.prepare(`
      UPDATE invitations 
      SET status = 'expired' 
      WHERE admin_id = ? AND status = 'pending' AND expires_at < datetime('now')
    `).bind(adminId).run();

    const response: InvitationsResponse = {
      success: true,
      data: {
        invitations: invitations.results || [],
        total
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching invitations:', error);
    const response: InvitationsResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
