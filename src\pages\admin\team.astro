---
import DashboardLayout from '../../layouts/DashboardLayout.astro';
import { TeamManagementPage } from '../../components/admin/TeamManagementPage';
import { isAdmin, getUserIdFromRequest } from '../../lib/auth-utils';
import { getDatabase } from '../../lib/database';

export const prerender = false;

// Check if user is authenticated and has admin privileges
const request = Astro.request;
const locals = Astro.locals;

// Get database connection
const db = getDatabase(locals.runtime.env);

// Get user ID first
const userId = getUserIdFromRequest(request);

if (!userId) {
  // Redirect to login if not authenticated
  return Astro.redirect('/api/auth/google');
}

// Check admin privileges
const isUserAdmin = await isAdmin(request, db);

if (!isUserAdmin) {
  // Redirect to dashboard if not admin
  return Astro.redirect('/dashboard?error=admin_required');
}
---

<DashboardLayout title="Team Management - QRAnalytica">
  <TeamManagementPage client:load />
</DashboardLayout>

<style>
  /* Additional styles for team management page */
  .team-management-container {
    max-width: 1200px;
    margin: 0 auto;
  }
</style>
