import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';
import { getUserIdFromRequest } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Parse request body
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation token is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user ID from request (user must be authenticated)
    const userId = getUserIdFromRequest(request);
    if (!userId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User must be authenticated to accept invitation'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Find the invitation
    const invitation = await db.prepare(`
      SELECT * FROM share_invitations 
      WHERE token = ? AND status = 'pending' AND expires_at > datetime('now')
    `).bind(token).first();

    if (!invitation) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid or expired invitation token'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user details to verify email matches
    const user = await db.prepare(`
      SELECT email FROM users WHERE id = ?
    `).bind(userId).first();

    if (!user || user.email !== invitation.email) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation email does not match authenticated user'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if dashboard is already shared with this user
    const existingShare = await db.prepare(`
      SELECT id FROM dashboard_shares 
      WHERE owner_id = ? AND shared_with_id = ? AND status = 'active'
    `).bind(invitation.owner_id, userId).first();

    if (existingShare) {
      // Mark invitation as accepted even if already shared
      await db.prepare(`
        UPDATE share_invitations 
        SET status = 'accepted', updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(invitation.id).run();

      return new Response(JSON.stringify({
        success: false,
        error: 'Dashboard is already shared with you'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create dashboard share relationship
    const shareId = uuidv4();
    await db.prepare(`
      INSERT INTO dashboard_shares (id, owner_id, shared_with_id, permission_level, status)
      VALUES (?, ?, ?, ?, 'active')
    `).bind(
      shareId,
      invitation.owner_id,
      userId,
      invitation.permission_level
    ).run();

    // Mark invitation as accepted
    await db.prepare(`
      UPDATE share_invitations 
      SET status = 'accepted', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(invitation.id).run();

    // Get owner details for response
    const owner = await db.prepare(`
      SELECT name, email FROM users WHERE id = ?
    `).bind(invitation.owner_id).first();

    return new Response(JSON.stringify({
      success: true,
      message: 'Dashboard sharing invitation accepted successfully',
      data: {
        owner_name: owner?.name || owner?.email,
        permission_level: invitation.permission_level
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error accepting dashboard sharing invitation:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
