import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Share2, Mail, Shield } from 'lucide-react';
import { useToast } from '../ui/use-toast';
import type { PermissionLevel, ShareDashboardResponse } from '../../types/dashboard';

interface ShareDashboardFormProps {
  onShareSent: () => void;
}

export const ShareDashboardForm: React.FC<ShareDashboardFormProps> = ({ onShareSent }) => {
  const [email, setEmail] = useState('');
  const [permissionLevel, setPermissionLevel] = useState<PermissionLevel>('VIEW');
  const [isLoading, setIsLoading] = useState(false);
  const [invitationUrl, setInvitationUrl] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !permissionLevel) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/team/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          permission_level: permissionLevel,
        }),
      });

      const data: InviteTeamMemberResponse = await response.json();

      if (data.success && data.data) {
        toast({
          title: "Invitation Sent",
          description: `Team member invitation sent to ${email}`,
        });
        
        setInvitationUrl(data.data.invitation_url);
        setEmail('');
        setPermissionLevel('VIEW');
        onInviteSent();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to send invitation",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast({
        title: "Error",
        description: "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyInvitationUrl = () => {
    if (invitationUrl) {
      navigator.clipboard.writeText(invitationUrl);
      toast({
        title: "Copied",
        description: "Invitation URL copied to clipboard",
      });
    }
  };

  const getPermissionDescription = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return 'Can view QR codes and analytics but cannot make changes';
      case 'EDIT':
        return 'Can view and modify QR codes and their settings';
      case 'DELETE':
        return 'Full access including ability to delete QR codes';
      default:
        return '';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <UserPlus className="h-5 w-5" />
          <span>Invite Team Member</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center space-x-2">
              <Mail className="h-4 w-4" />
              <span>Email Address</span>
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter team member's email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label className="flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Permission Level</span>
            </Label>
            <Select value={permissionLevel} onValueChange={(value: PermissionLevel) => setPermissionLevel(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select permission level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="VIEW">
                  <div className="flex flex-col">
                    <span className="font-medium">View Only</span>
                    <span className="text-sm text-muted-foreground">Can view QR codes and analytics</span>
                  </div>
                </SelectItem>
                <SelectItem value="EDIT">
                  <div className="flex flex-col">
                    <span className="font-medium">Edit Access</span>
                    <span className="text-sm text-muted-foreground">Can view and modify QR codes</span>
                  </div>
                </SelectItem>
                <SelectItem value="DELETE">
                  <div className="flex flex-col">
                    <span className="font-medium">Full Access</span>
                    <span className="text-sm text-muted-foreground">Can view, modify, and delete QR codes</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              {getPermissionDescription(permissionLevel)}
            </p>
          </div>

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? 'Sending Invitation...' : 'Send Invitation'}
          </Button>
        </form>

        {invitationUrl && (
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <Label className="text-sm font-medium">Invitation URL</Label>
            <div className="flex items-center space-x-2 mt-2">
              <Input
                value={invitationUrl}
                readOnly
                className="text-sm"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={copyInvitationUrl}
              >
                Copy
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Share this URL with the team member to accept the invitation.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
