-- User-to-User Dashboard Sharing Migration
-- Migration: 0005_user_to_user_sharing.sql
-- Transforms admin-only team management to peer-to-peer dashboard sharing

-- Remove admin column from users table (no longer needed)
ALTER TABLE users DROP COLUMN is_admin;

-- Rename team_members to dashboard_shares and update structure
-- First create the new table with updated schema
CREATE TABLE IF NOT EXISTS dashboard_shares (
  id TEXT PRIMARY KEY,
  owner_id TEXT NOT NULL,           -- User who owns the dashboard
  shared_with_id TEXT NOT NULL,     -- User who gets access to the dashboard
  permission_level TEXT NOT NULL CHECK (permission_level IN ('VIEW', 'EDIT', 'DELETE')),
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  <PERSON>OREI<PERSON><PERSON> KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
  <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (shared_with_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE(owner_id, shared_with_id)  -- One sharing relationship per user pair
);

-- Migrate existing team_members data to dashboard_shares
-- admin_id becomes owner_id, member_id becomes shared_with_id
INSERT INTO dashboard_shares (id, owner_id, shared_with_id, permission_level, status, created_at, updated_at)
SELECT id, admin_id, member_id, permission_level, status, created_at, updated_at
FROM team_members;

-- Drop the old team_members table
DROP TABLE team_members;

-- Rename invitations to share_invitations and update structure
-- First create the new table with updated schema
CREATE TABLE IF NOT EXISTS share_invitations (
  id TEXT PRIMARY KEY,
  owner_id TEXT NOT NULL,           -- User who is sharing their dashboard
  email TEXT NOT NULL,              -- Email of person being invited
  permission_level TEXT NOT NULL CHECK (permission_level IN ('VIEW', 'EDIT', 'DELETE')),
  token TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  expires_at DATETIME NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Migrate existing invitations data to share_invitations
-- admin_id becomes owner_id
INSERT INTO share_invitations (id, owner_id, email, permission_level, token, status, expires_at, created_at, updated_at)
SELECT id, admin_id, email, permission_level, token, status, expires_at, created_at, updated_at
FROM invitations;

-- Drop the old invitations table
DROP TABLE invitations;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dashboard_shares_owner_id ON dashboard_shares(owner_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_shares_shared_with_id ON dashboard_shares(shared_with_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_shares_status ON dashboard_shares(status);
CREATE INDEX IF NOT EXISTS idx_share_invitations_owner_id ON share_invitations(owner_id);
CREATE INDEX IF NOT EXISTS idx_share_invitations_token ON share_invitations(token);
CREATE INDEX IF NOT EXISTS idx_share_invitations_email ON share_invitations(email);
CREATE INDEX IF NOT EXISTS idx_share_invitations_status ON share_invitations(status);

-- Create triggers to update updated_at timestamp for dashboard_shares
CREATE TRIGGER IF NOT EXISTS update_dashboard_shares_updated_at
  AFTER UPDATE ON dashboard_shares
  FOR EACH ROW
  BEGIN
    UPDATE dashboard_shares SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
  END;

-- Create triggers to update updated_at timestamp for share_invitations
CREATE TRIGGER IF NOT EXISTS update_share_invitations_updated_at
  AFTER UPDATE ON share_invitations
  FOR EACH ROW
  BEGIN
    UPDATE share_invitations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
  END;
