import type { APIRoute } from 'astro';
import { getUserFromRequest, getUserIdFromRequest, isAdmin } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Get user info
    const user = getUserFromRequest(request);
    const userId = getUserIdFromRequest(request);
    const isUserAdmin = await isAdmin(request, db);

    // Get user from database
    let dbUser = null;
    if (userId) {
      dbUser = await db.prepare(
        'SELECT id, email, name, is_admin FROM users WHERE id = ? OR email = ?'
      ).bind(userId, userId).first();
    }

    return new Response(JSON.stringify({
      user_from_request: user,
      user_id_from_request: userId,
      is_admin_check: isUserAdmin,
      user_from_database: dbUser,
      debug_info: {
        has_user: !!user,
        has_user_id: !!userId,
        user_id_type: typeof userId,
        user_email: user?.email,
        user_name: user?.name
      }
    }, null, 2), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Debug error:', error);
    return new Response(JSON.stringify({
      error: error.message,
      stack: error.stack
    }, null, 2), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
