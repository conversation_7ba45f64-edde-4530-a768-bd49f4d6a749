import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';
import { getUserIdFromRequest } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';
import type { 
  ShareDashboardRequest, 
  ShareDashboardResponse,
  PermissionLevel 
} from '../../../types/dashboard';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Get current user (dashboard owner)
    const ownerId = getUserIdFromRequest(request);
    if (!ownerId) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Authentication required'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body: ShareDashboardRequest = await request.json();
    const { email, permission_level } = body;

    // Validate input
    if (!email || !permission_level) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Email and permission level are required'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate permission level
    const validPermissions: PermissionLevel[] = ['VIEW', 'EDIT', 'DELETE'];
    if (!validPermissions.includes(permission_level)) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Invalid permission level'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if user is trying to share with themselves
    const owner = await db.prepare(
      'SELECT email FROM users WHERE id = ?'
    ).bind(ownerId).first();

    if (owner?.email === email) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Cannot share dashboard with yourself'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if dashboard is already shared with this user
    const existingShare = await db.prepare(`
      SELECT ds.id 
      FROM dashboard_shares ds
      JOIN users u ON ds.shared_with_id = u.id
      WHERE ds.owner_id = ? AND u.email = ? AND ds.status = 'active'
    `).bind(ownerId, email).first();

    if (existingShare) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Dashboard is already shared with this user'
      };
      return new Response(JSON.stringify(response), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if there's already a pending invitation
    const existingInvitation = await db.prepare(`
      SELECT id 
      FROM share_invitations 
      WHERE owner_id = ? AND email = ? AND status = 'pending' AND expires_at > datetime('now')
    `).bind(ownerId, email).first();

    if (existingInvitation) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Pending invitation already exists for this email'
      };
      return new Response(JSON.stringify(response), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate invitation token and expiration (7 days from now)
    const invitationId = uuidv4();
    const token = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create invitation
    await db.prepare(`
      INSERT INTO share_invitations (id, owner_id, email, permission_level, token, expires_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      invitationId,
      ownerId,
      email,
      permission_level,
      token,
      expiresAt.toISOString()
    ).run();

    // Get the created invitation
    const invitation = await db.prepare(`
      SELECT * FROM share_invitations WHERE id = ?
    `).bind(invitationId).first();

    if (!invitation) {
      const response: ShareDashboardResponse = {
        success: false,
        error: 'Failed to create invitation'
      };
      return new Response(JSON.stringify(response), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate invitation URL
    const baseUrl = new URL(request.url).origin;
    const invitationUrl = `${baseUrl}/sharing/accept-invitation?token=${token}`;

    const response: ShareDashboardResponse = {
      success: true,
      data: {
        invitation: invitation as any,
        invitation_url: invitationUrl
      }
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error sharing dashboard:', error);
    const response: ShareDashboardResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
