import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Users, 
  UserPlus, 
  Search, 
  RefreshCw,
  Mail,
  Shield
} from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { InviteMemberForm } from './InviteMemberForm';
import { TeamMembersTable } from './TeamMembersTable';
import { PendingInvitationsTable } from './PendingInvitationsTable';
import type { 
  TeamMember, 
  Invitation, 
  TeamMembersResponse, 
  InvitationsResponse 
} from '../../types/dashboard';

export const TeamManagementPage: React.FC = () => {
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingMembers, setIsLoadingMembers] = useState(true);
  const [isLoadingInvitations, setIsLoadingInvitations] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchTeamMembers = async () => {
    try {
      const response = await fetch(`/api/admin/team/members?search=${encodeURIComponent(searchQuery)}`);
      const data: TeamMembersResponse = await response.json();

      if (data.success && data.data) {
        setMembers(data.data.members);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch team members",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching team members:', error);
      toast({
        title: "Error",
        description: "Failed to fetch team members",
        variant: "destructive",
      });
    } finally {
      setIsLoadingMembers(false);
    }
  };

  const fetchInvitations = async () => {
    try {
      const response = await fetch('/api/admin/team/invitations');
      const data: InvitationsResponse = await response.json();

      if (data.success && data.data) {
        setInvitations(data.data.invitations);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch invitations",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch invitations",
        variant: "destructive",
      });
    } finally {
      setIsLoadingInvitations(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([fetchTeamMembers(), fetchInvitations()]);
    setIsRefreshing(false);
  };

  const handleInviteSent = () => {
    fetchInvitations();
  };

  const handleMemberUpdated = () => {
    fetchTeamMembers();
  };

  const handleMemberRemoved = () => {
    fetchTeamMembers();
  };

  const handleInvitationResent = () => {
    fetchInvitations();
  };

  useEffect(() => {
    fetchTeamMembers();
    fetchInvitations();
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (!isLoadingMembers) {
        fetchTeamMembers();
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const filteredMembers = members.filter(member =>
    member.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Team Management</h1>
          <p className="text-muted-foreground">
            Invite and manage team members with granular permissions
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Team Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{members.length}</div>
            <p className="text-xs text-muted-foreground">
              Active team members
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invitations</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invitations.length}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting acceptance
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permission Levels</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              View, Edit, Delete
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Invite Form */}
        <div className="lg:col-span-1">
          <InviteMemberForm onInviteSent={handleInviteSent} />
        </div>

        {/* Team Members and Invitations */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="members" className="space-y-4">
            <TabsList>
              <TabsTrigger value="members" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Team Members ({members.length})</span>
              </TabsTrigger>
              <TabsTrigger value="invitations" className="flex items-center space-x-2">
                <UserPlus className="h-4 w-4" />
                <span>Pending Invitations ({invitations.length})</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="members" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Team Members</CardTitle>
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                          placeholder="Search members..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10 w-64"
                        />
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoadingMembers ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <TeamMembersTable
                      members={filteredMembers}
                      onMemberUpdated={handleMemberUpdated}
                      onMemberRemoved={handleMemberRemoved}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="invitations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pending Invitations</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingInvitations ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <PendingInvitationsTable
                      invitations={invitations}
                      onInvitationResent={handleInvitationResent}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
