import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import { 
  MoreVertical, 
  Send, 
  Copy, 
  Clock,
  Mail,
  Calendar
} from 'lucide-react';
import { useToast } from '../ui/use-toast';
import type { Invitation, PermissionLevel } from '../../types/dashboard';

interface PendingInvitationsTableProps {
  invitations: Invitation[];
  onInvitationResent: () => void;
}

export const PendingInvitationsTable: React.FC<PendingInvitationsTableProps> = ({
  invitations,
  onInvitationResent,
}) => {
  const [resendingId, setResendingId] = useState<string | null>(null);
  const { toast } = useToast();

  const getPermissionBadgeVariant = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return 'secondary';
      case 'EDIT':
        return 'default';
      case 'DELETE':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPermissionIcon = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return '👁️';
      case 'EDIT':
        return '✏️';
      case 'DELETE':
        return '🗑️';
      default:
        return '👁️';
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    setResendingId(invitationId);
    
    try {
      const response = await fetch(`/api/admin/team/invitations/${invitationId}/resend`, {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Invitation Resent",
          description: "Invitation has been resent successfully",
        });
        onInvitationResent();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to resend invitation",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error resending invitation:', error);
      toast({
        title: "Error",
        description: "Failed to resend invitation",
        variant: "destructive",
      });
    } finally {
      setResendingId(null);
    }
  };

  const copyInvitationUrl = (token: string) => {
    const baseUrl = window.location.origin;
    const invitationUrl = `${baseUrl}/admin/team/accept-invitation?token=${token}`;
    
    navigator.clipboard.writeText(invitationUrl);
    toast({
      title: "Copied",
      description: "Invitation URL copied to clipboard",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  const getStatusBadge = (invitation: Invitation) => {
    if (isExpired(invitation.expires_at)) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    
    switch (invitation.status) {
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'accepted':
        return <Badge variant="default">Accepted</Badge>;
      case 'expired':
        return <Badge variant="destructive">Expired</Badge>;
      default:
        return <Badge variant="secondary">{invitation.status}</Badge>;
    }
  };

  if (invitations.length === 0) {
    return (
      <div className="text-center py-12">
        <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-muted-foreground">No Pending Invitations</h3>
        <p className="text-muted-foreground">All invitations have been accepted or expired.</p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead>Permission Level</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Sent</TableHead>
            <TableHead>Expires</TableHead>
            <TableHead className="w-[50px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invitations.map((invitation) => (
            <TableRow key={invitation.id}>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {invitation.email.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <div className="font-medium">{invitation.email}</div>
                    <div className="text-sm text-muted-foreground">Invitation pending</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={getPermissionBadgeVariant(invitation.permission_level)}>
                  <span className="mr-1">{getPermissionIcon(invitation.permission_level)}</span>
                  {invitation.permission_level}
                </Badge>
              </TableCell>
              <TableCell>
                {getStatusBadge(invitation)}
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(invitation.created_at)}</span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>{formatDateTime(invitation.expires_at)}</span>
                </div>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem 
                      onClick={() => handleResendInvitation(invitation.id)}
                      disabled={resendingId === invitation.id}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      {resendingId === invitation.id ? 'Resending...' : 'Resend Invitation'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => copyInvitationUrl(invitation.token)}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Invitation URL
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
