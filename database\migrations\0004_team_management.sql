-- Team Management System Migration
-- Migration: 0004_team_management.sql

-- Add admin flag to users table
ALTER TABLE users ADD COLUMN is_admin INTEGER DEFAULT 0;

-- Create team_members table to manage team relationships and permissions
CREATE TABLE IF NOT EXISTS team_members (
  id TEXT PRIMARY KEY,
  admin_id TEXT NOT NULL,
  member_id TEXT NOT NULL,
  permission_level TEXT NOT NULL CHECK (permission_level IN ('VIEW', 'EDIT', 'DELETE')),
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (member_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE(admin_id, member_id)
);

-- Create invitations table to track pending team member invitations
CREATE TABLE IF NOT EXISTS invitations (
  id TEXT PRIMARY KEY,
  admin_id TEXT NOT NULL,
  email TEXT NOT NULL,
  permission_level TEXT NOT NULL CHECK (permission_level IN ('VIEW', 'EDIT', 'DELETE')),
  token TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  expires_at DATETIME NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_team_members_admin_id ON team_members(admin_id);
CREATE INDEX IF NOT EXISTS idx_team_members_member_id ON team_members(member_id);
CREATE INDEX IF NOT EXISTS idx_invitations_admin_id ON invitations(admin_id);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_status ON invitations(status);

-- Create trigger to update updated_at timestamp for team_members
CREATE TRIGGER IF NOT EXISTS update_team_members_updated_at
  AFTER UPDATE ON team_members
  FOR EACH ROW
  BEGIN
    UPDATE team_members SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
  END;

-- Create trigger to update updated_at timestamp for invitations
CREATE TRIGGER IF NOT EXISTS update_invitations_updated_at
  AFTER UPDATE ON invitations
  FOR EACH ROW
  BEGIN
    UPDATE invitations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
  END;
