import type { APIRoute } from 'astro';
import { getUserIdFromRequest } from '../../../../lib/auth-utils';
import { getDatabase } from '../../../../lib/database';
import type { 
  UpdateSharePermissionsRequest, 
  UpdateSharePermissionsResponse,
  PermissionLevel 
} from '../../../../types/dashboard';

export const prerender = false;

export const PUT: APIRoute = async ({ params, request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    const shareId = params.id;

    if (!shareId) {
      const response: UpdateSharePermissionsResponse = {
        success: false,
        error: 'Share ID is required'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get current user (must be dashboard owner)
    const ownerId = getUserIdFromRequest(request);
    if (!ownerId) {
      const response: UpdateSharePermissionsResponse = {
        success: false,
        error: 'Authentication required'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body: UpdateSharePermissionsRequest = await request.json();
    const { permission_level } = body;

    // Validate permission level
    const validPermissions: PermissionLevel[] = ['VIEW', 'EDIT', 'DELETE'];
    if (!validPermissions.includes(permission_level)) {
      const response: UpdateSharePermissionsResponse = {
        success: false,
        error: 'Invalid permission level'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the share belongs to this user (owner)
    const existingShare = await db.prepare(`
      SELECT id FROM dashboard_shares 
      WHERE id = ? AND owner_id = ?
    `).bind(shareId, ownerId).first();

    if (!existingShare) {
      const response: UpdateSharePermissionsResponse = {
        success: false,
        error: 'Share not found or access denied'
      };
      return new Response(JSON.stringify(response), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update permissions
    await db.prepare(`
      UPDATE dashboard_shares 
      SET permission_level = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND owner_id = ?
    `).bind(permission_level, shareId, ownerId).run();

    // Get updated share with user details
    const updatedShare = await db.prepare(`
      SELECT 
        ds.id,
        ds.owner_id,
        ds.shared_with_id,
        owner.email as owner_email,
        owner.name as owner_name,
        owner.picture as owner_picture,
        u.email as shared_with_email,
        u.name as shared_with_name,
        u.picture as shared_with_picture,
        ds.permission_level,
        ds.status,
        ds.created_at,
        ds.updated_at
      FROM dashboard_shares ds
      JOIN users u ON ds.shared_with_id = u.id
      JOIN users owner ON ds.owner_id = owner.id
      WHERE ds.id = ? AND ds.owner_id = ?
    `).bind(shareId, ownerId).first();

    if (!updatedShare) {
      const response: UpdateSharePermissionsResponse = {
        success: false,
        error: 'Failed to retrieve updated share'
      };
      return new Response(JSON.stringify(response), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const response: UpdateSharePermissionsResponse = {
      success: true,
      data: updatedShare as any
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error updating share permissions:', error);
    const response: UpdateSharePermissionsResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const DELETE: APIRoute = async ({ params, request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    const shareId = params.id;

    if (!shareId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Share ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get current user (must be dashboard owner)
    const ownerId = getUserIdFromRequest(request);
    if (!ownerId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the share belongs to this user (owner)
    const existingShare = await db.prepare(`
      SELECT id FROM dashboard_shares 
      WHERE id = ? AND owner_id = ?
    `).bind(shareId, ownerId).first();

    if (!existingShare) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Share not found or access denied'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete share (revoke access)
    await db.prepare(`
      DELETE FROM dashboard_shares 
      WHERE id = ? AND owner_id = ?
    `).bind(shareId, ownerId).run();

    return new Response(JSON.stringify({
      success: true,
      message: 'Dashboard access revoked successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error revoking dashboard access:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
