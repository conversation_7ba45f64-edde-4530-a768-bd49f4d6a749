import type { APIRoute } from 'astro';
import { requireAdmin, getUserIdFromRequest } from '../../../../../lib/auth-utils';
import { getDatabase } from '../../../../../lib/database';
import type { 
  UpdatePermissionsRequest, 
  UpdatePermissionsResponse,
  PermissionLevel 
} from '../../../../../types/dashboard';

export const prerender = false;

export const PUT: APIRoute = async ({ params, request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    const memberId = params.id;

    if (!memberId) {
      const response: UpdatePermissionsResponse = {
        success: false,
        error: 'Member ID is required'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check admin privileges
    const adminCheck = await requireAdmin(request, db);
    if (!adminCheck.success) {
      const response: UpdatePermissionsResponse = {
        success: false,
        error: adminCheck.error || 'Admin privileges required'
      };
      return new Response(JSON.stringify(response), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get admin user ID
    const adminId = getUserIdFromRequest(request);
    if (!adminId) {
      const response: UpdatePermissionsResponse = {
        success: false,
        error: 'Admin user not found'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body: UpdatePermissionsRequest = await request.json();
    const { permission_level } = body;

    // Validate permission level
    const validPermissions: PermissionLevel[] = ['VIEW', 'EDIT', 'DELETE'];
    if (!validPermissions.includes(permission_level)) {
      const response: UpdatePermissionsResponse = {
        success: false,
        error: 'Invalid permission level'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the team member belongs to this admin
    const existingMember = await db.prepare(`
      SELECT id FROM team_members 
      WHERE id = ? AND admin_id = ?
    `).bind(memberId, adminId).first();

    if (!existingMember) {
      const response: UpdatePermissionsResponse = {
        success: false,
        error: 'Team member not found or access denied'
      };
      return new Response(JSON.stringify(response), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update permissions
    await db.prepare(`
      UPDATE team_members 
      SET permission_level = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND admin_id = ?
    `).bind(permission_level, memberId, adminId).run();

    // Get updated team member with user details
    const updatedMember = await db.prepare(`
      SELECT 
        tm.id,
        tm.admin_id,
        tm.member_id,
        u.email,
        u.name,
        u.picture,
        tm.permission_level,
        tm.status,
        tm.created_at,
        tm.updated_at
      FROM team_members tm
      JOIN users u ON tm.member_id = u.id
      WHERE tm.id = ? AND tm.admin_id = ?
    `).bind(memberId, adminId).first();

    if (!updatedMember) {
      const response: UpdatePermissionsResponse = {
        success: false,
        error: 'Failed to retrieve updated member'
      };
      return new Response(JSON.stringify(response), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const response: UpdatePermissionsResponse = {
      success: true,
      data: updatedMember as any
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error updating team member permissions:', error);
    const response: UpdatePermissionsResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const DELETE: APIRoute = async ({ params, request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    const memberId = params.id;

    if (!memberId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Member ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check admin privileges
    const adminCheck = await requireAdmin(request, db);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error || 'Admin privileges required'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get admin user ID
    const adminId = getUserIdFromRequest(request);
    if (!adminId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Admin user not found'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the team member belongs to this admin
    const existingMember = await db.prepare(`
      SELECT id FROM team_members 
      WHERE id = ? AND admin_id = ?
    `).bind(memberId, adminId).first();

    if (!existingMember) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Team member not found or access denied'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete team member
    await db.prepare(`
      DELETE FROM team_members 
      WHERE id = ? AND admin_id = ?
    `).bind(memberId, adminId).run();

    return new Response(JSON.stringify({
      success: true,
      message: 'Team member removed successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error removing team member:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
