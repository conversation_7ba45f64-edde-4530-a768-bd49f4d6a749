---
import DashboardLayout from '../../layouts/DashboardLayout.astro';
import { DashboardSharingPage } from '../../components/sharing/DashboardSharingPage';
import { getUserIdFromRequest } from '../../lib/auth-utils';

export const prerender = false;

// Check if user is authenticated
const request = Astro.request;
const userId = getUserIdFromRequest(request);

if (!userId) {
  // Redirect to login if not authenticated
  return Astro.redirect('/api/auth/google');
}
---

<DashboardLayout title="Dashboard Sharing - QRAnalytica">
  <DashboardSharingPage client:load />
</DashboardLayout>

<style>
  /* Additional styles for dashboard sharing page */
  .dashboard-sharing-container {
    max-width: 1200px;
    margin: 0 auto;
  }
</style>
