export interface QRCode {
  id: string;
  user_id: string | null;
  name: string | null;
  data: string;
  dynamic: number;
  tracking_domain: string | null;
  custom_slug: string | null;
  redirect_url: string | null;
  content_type: string;
  content_data: string | null;
  original_url: string | null;
  email_address: string | null;
  wifi_ssid: string | null;
  created_at: string;
  updated_at: string | null;
  scanCount: number;
  status: 'active' | 'inactive';
  lastScanned?: string;
}

export interface DashboardMetrics {
  totalActiveQRCodes: number;
  totalScanCount: number;
  todayScanCount: number;
  weeklyGrowth?: number;
  monthlyGrowth?: number;
}

export interface QRAnalytics {
  qrCodeId: string;
  totalScans: number;
  uniqueScans: number;
  scansByDate: ScanByDate[];
  scansByLocation: ScanByLocation[];
  scansByDevice: ScanByDevice[];
  scansByReferrer: ScanByReferrer[];
  recentScans: RecentScan[];
}

export interface ScanByDate {
  date: string;
  scans: number;
}

export interface ScanByLocation {
  country: string;
  city?: string;
  scans: number;
  percentage: number;
}

export interface ScanByDevice {
  device: string;
  scans: number;
  percentage: number;
}

export interface ScanByReferrer {
  referrer: string;
  scans: number;
  percentage: number;
}

export interface RecentScan {
  id: string;
  qr_code_id: string;
  scan_time: string;
  ip: string | null;
  user_agent: string | null;
  referrer: string | null;
  lat: number | null;
  lon: number | null;
  city: string | null;
  country: string | null;
  device: string | null;
  os: string | null;
  browser: string | null;
  created_at: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface QRCodesTableProps {
  qrCodes: QRCode[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onViewDetails: (qrCodeId: string) => void;
  onDownloadQR: (qrCodeId: string) => void;
}

// Database-specific types
export interface DatabaseQRCode {
  id: string;
  user_id: string | null;
  name: string | null;
  data: string;
  dynamic: number;
  tracking_domain: string | null;
  custom_slug: string | null;
  redirect_url: string | null;
  content_type: string;
  content_data: string | null;
  original_url: string | null;
  email_address: string | null;
  wifi_ssid: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface DatabaseScanAnalytics {
  id: string;
  qr_code_id: string;
  scan_time: string;
  ip: string | null;
  user_agent: string | null;
  referrer: string | null;
  lat: number | null;
  lon: number | null;
  city: string | null;
  country: string | null;
  device: string | null;
  os: string | null;
  browser: string | null;
  created_at: string;
}

// Dashboard Sharing Types
export type PermissionLevel = 'VIEW' | 'EDIT' | 'DELETE';
export type ShareStatus = 'active' | 'inactive';
export type InvitationStatus = 'pending' | 'accepted' | 'expired';

export interface DashboardShare {
  id: string;
  owner_id: string;
  shared_with_id: string;
  owner_email: string;
  owner_name: string | null;
  owner_picture: string | null;
  shared_with_email: string;
  shared_with_name: string | null;
  shared_with_picture: string | null;
  permission_level: PermissionLevel;
  status: ShareStatus;
  created_at: string;
  updated_at: string;
}

export interface ShareInvitation {
  id: string;
  owner_id: string;
  email: string;
  permission_level: PermissionLevel;
  token: string;
  status: InvitationStatus;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export interface DashboardContext {
  owner_id: string;
  owner_name: string | null;
  owner_email: string;
  is_own_dashboard: boolean;
  permission_level?: PermissionLevel;
}

export interface SharedDashboard {
  owner_id: string;
  owner_name: string | null;
  owner_email: string;
  permission_level: PermissionLevel;
  shared_at: string;
}

// API Response Types for Dashboard Sharing
export interface DashboardSharesResponse {
  success: boolean;
  data?: {
    shares: DashboardShare[];
    total: number;
  };
  error?: string;
}

export interface ShareInvitationsResponse {
  success: boolean;
  data?: {
    invitations: ShareInvitation[];
    total: number;
  };
  error?: string;
}

export interface SharedDashboardsResponse {
  success: boolean;
  data?: {
    dashboards: SharedDashboard[];
    total: number;
  };
  error?: string;
}

export interface ShareDashboardRequest {
  email: string;
  permission_level: PermissionLevel;
}

export interface ShareDashboardResponse {
  success: boolean;
  data?: {
    invitation: ShareInvitation;
    invitation_url: string;
  };
  error?: string;
}

export interface UpdateSharePermissionsRequest {
  permission_level: PermissionLevel;
}

export interface UpdateSharePermissionsResponse {
  success: boolean;
  data?: DashboardShare;
  error?: string;
}

export interface DashboardContextResponse {
  success: boolean;
  data?: DashboardContext;
  error?: string;
}

// API Response types
export interface DashboardMetricsResponse {
  success: boolean;
  data?: DashboardMetrics;
  error?: string;
}

export interface QRCodesListResponse {
  success: boolean;
  data?: {
    qrCodes: QRCode[];
    pagination: PaginationInfo;
  };
  error?: string;
}

export interface QRAnalyticsResponse {
  success: boolean;
  data?: {
    qrCode: QRCode;
    analytics: QRAnalytics;
  };
  error?: string;
}
