import type { APIRoute } from 'astro';
import { getUserIdFromRequest } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';
import type { SharedDashboardsResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Get current user
    const userId = getUserIdFromRequest(request);
    if (!userId) {
      const response: SharedDashboardsResponse = {
        success: false,
        error: 'Authentication required'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get query parameters for pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const offset = (page - 1) * limit;

    // Build search condition
    let searchCondition = '';
    let searchParams = [userId];
    
    if (search) {
      searchCondition = 'AND (owner.name LIKE ? OR owner.email LIKE ?)';
      searchParams.push(`%${search}%`, `%${search}%`);
    }

    // Get dashboards shared with me
    const dashboardsQuery = `
      SELECT 
        ds.owner_id,
        owner.name as owner_name,
        owner.email as owner_email,
        ds.permission_level,
        ds.created_at as shared_at
      FROM dashboard_shares ds
      JOIN users owner ON ds.owner_id = owner.id
      WHERE ds.shared_with_id = ? AND ds.status = 'active' ${searchCondition}
      ORDER BY owner.name, owner.email
      LIMIT ? OFFSET ?
    `;

    const dashboards = await db.prepare(dashboardsQuery)
      .bind(...searchParams, limit, offset)
      .all();

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM dashboard_shares ds
      JOIN users owner ON ds.owner_id = owner.id
      WHERE ds.shared_with_id = ? AND ds.status = 'active' ${searchCondition}
    `;

    const countResult = await db.prepare(countQuery)
      .bind(...searchParams)
      .first();

    const total = countResult?.total || 0;

    const response: SharedDashboardsResponse = {
      success: true,
      data: {
        dashboards: dashboards.results || [],
        total
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching shared dashboards:', error);
    const response: SharedDashboardsResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
