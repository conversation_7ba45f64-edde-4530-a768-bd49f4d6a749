---
import DashboardLayout from '../../layouts/DashboardLayout.astro';
import { getUserIdFromRequest } from '../../lib/auth-utils';
import { getDatabase } from '../../lib/database';

export const prerender = false;

const request = Astro.request;
const locals = Astro.locals;
const url = new URL(request.url);
const token = url.searchParams.get('token');

// Get database connection
const db = getDatabase(locals.runtime.env);

// Check if user is authenticated
const userId = getUserIdFromRequest(request);

let invitation = null;
let error = null;
let success = false;

if (!token) {
  error = 'Invalid invitation link';
} else if (!userId) {
  // Store token in session and redirect to login
  return Astro.redirect(`/api/auth/google?redirect=/sharing/accept-invitation?token=${token}`);
} else {
  // Try to get invitation details
  try {
    invitation = await db.prepare(`
      SELECT i.*, u.name as owner_name, u.email as owner_email
      FROM share_invitations i
      JOIN users u ON i.owner_id = u.id
      WHERE i.token = ? AND i.status = 'pending' AND i.expires_at > datetime('now')
    `).bind(token).first();

    if (!invitation) {
      error = 'Invitation not found or has expired';
    }
  } catch (err) {
    console.error('Error fetching invitation:', err);
    error = 'Failed to process invitation';
  }
}
---

<DashboardLayout title="Accept Dashboard Sharing Invitation - QRAnalytica">
  <div class="max-w-2xl mx-auto">
    {error ? (
      <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <h1 class="text-2xl font-bold text-red-900 mb-2">Invalid Invitation</h1>
        <p class="text-red-700 mb-4">{error}</p>
        <a href="/dashboard" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
          Go to Dashboard
        </a>
      </div>
    ) : invitation ? (
      <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200">
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
              </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Dashboard Sharing Invitation</h1>
            <p class="text-gray-600">You've been invited to access a QRAnalytica dashboard</p>
          </div>
        </div>

        <div class="p-6">
          <div class="space-y-4">
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="font-medium text-gray-900 mb-2">Invitation Details</h3>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">From:</span>
                  <span class="font-medium">{invitation.owner_name || invitation.owner_email}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Dashboard Owner:</span>
                  <span class="font-medium">{invitation.owner_email}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Your Email:</span>
                  <span class="font-medium">{invitation.email}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Permission Level:</span>
                  <span class="font-medium">
                    {invitation.permission_level === 'VIEW' && '👁️ View Only'}
                    {invitation.permission_level === 'EDIT' && '✏️ Edit Access'}
                    {invitation.permission_level === 'DELETE' && '🗑️ Full Access'}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Expires:</span>
                  <span class="font-medium">{new Date(invitation.expires_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            <div class="bg-blue-50 rounded-lg p-4">
              <h4 class="font-medium text-blue-900 mb-2">What you'll be able to do:</h4>
              <ul class="text-sm text-blue-800 space-y-1">
                {invitation.permission_level === 'VIEW' && (
                  <>
                    <li>• View QR codes and analytics</li>
                    <li>• Download QR codes</li>
                    <li>• Access reports and insights</li>
                  </>
                )}
                {invitation.permission_level === 'EDIT' && (
                  <>
                    <li>• View QR codes and analytics</li>
                    <li>• Create and edit QR codes</li>
                    <li>• Update QR code settings</li>
                    <li>• Download QR codes</li>
                  </>
                )}
                {invitation.permission_level === 'DELETE' && (
                  <>
                    <li>• Full access to all QR codes</li>
                    <li>• Create, edit, and delete QR codes</li>
                    <li>• Manage QR code lifecycle</li>
                    <li>• Access all analytics and reports</li>
                  </>
                )}
              </ul>
            </div>
          </div>

          <div class="mt-6 flex space-x-3">
            <button
              id="acceptInvitation"
              class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
            >
              Accept Invitation
            </button>
            <a
              href="/dashboard"
              class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors font-medium text-center"
            >
              Decline
            </a>
          </div>
        </div>
      </div>
    ) : (
      <div class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading invitation...</p>
      </div>
    )}
  </div>

  <script define:vars={{ token }}>
    document.getElementById('acceptInvitation')?.addEventListener('click', async () => {
      const button = document.getElementById('acceptInvitation');
      if (!button) return;

      button.textContent = 'Accepting...';
      button.disabled = true;

      try {
        const response = await fetch('/api/sharing/accept-invitation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (data.success) {
          // Show success message and redirect
          button.textContent = 'Accepted!';
          button.className = 'flex-1 bg-green-600 text-white px-4 py-2 rounded-md font-medium';
          
          setTimeout(() => {
            window.location.href = '/dashboard';
          }, 1500);
        } else {
          button.textContent = 'Accept Invitation';
          button.disabled = false;
          alert(data.error || 'Failed to accept invitation');
        }
      } catch (error) {
        console.error('Error accepting invitation:', error);
        button.textContent = 'Accept Invitation';
        button.disabled = false;
        alert('Failed to accept invitation');
      }
    });
  </script>
</DashboardLayout>
