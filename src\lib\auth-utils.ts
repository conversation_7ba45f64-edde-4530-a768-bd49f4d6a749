import type { User } from '../stores/authStore';

/**
 * Extract user information from request cookies
 */
export function getUserFromRequest(request: Request): User | null {
  try {
    const cookieHeader = request.headers.get('cookie');
    console.log('Debug: Cookie header:', cookieHeader);

    if (!cookieHeader) {
      console.log('Debug: No cookie header found');
      return null;
    }

    const cookies = cookieHeader.split('; ');
    const userCookie = cookies.find(cookie => cookie.startsWith('user='));
    console.log('Debug: User cookie found:', !!userCookie);

    if (!userCookie) {
      console.log('Debug: No user cookie found in:', cookies);
      return null;
    }

    const cookieValue = userCookie.split('=').slice(1).join('='); // Handle = in cookie value
    if (!cookieValue) {
      console.log('Debug: Empty cookie value');
      return null;
    }

    console.log('Debug: Raw cookie value:', cookieValue);

    // The cookie is double-encoded, so we need to decode it twice
    let jsonStr: string;
    try {
      const firstDecode = decodeURIComponent(cookieValue);
      jsonStr = decodeURIComponent(firstDecode);
      console.log('Debug: Decoded JSON string:', jsonStr);
    } catch (decodeError) {
      // If decoding fails, try using the raw value
      jsonStr = cookieValue;
      console.log('Debug: Using raw cookie value due to decode error:', decodeError);
    }

    const user = JSON.parse(jsonStr) as User;
    console.log('Debug: Parsed user:', { email: user.email, name: user.name, id: user.id });
    return user;
  } catch (error) {
    console.error('Error extracting user from request:', error);
    return null;
  }
}

/**
 * Get user ID from request, with fallback to email if ID is not available
 */
export function getUserIdFromRequest(request: Request): string | null {
  const user = getUserFromRequest(request);
  if (!user) return null;

  // Use user.id if available, otherwise fall back to email as identifier
  return user.id || user.email || null;
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(request: Request): boolean {
  const user = getUserFromRequest(request);
  const isAuth = user !== null;
  console.log('Debug: Authentication check result:', isAuth);
  return isAuth;
}

/**
 * Development helper - allows bypassing authentication in development
 */
export function isDevelopmentMode(): boolean {
  return process.env.NODE_ENV === 'development' ||
         typeof process !== 'undefined' && process.env.ASTRO_DEV === 'true';
}

/**
 * Check if user can access a specific dashboard
 */
export async function canAccessDashboard(
  request: Request,
  db: D1Database,
  dashboardOwnerId: string
): Promise<{ hasAccess: boolean; permissionLevel?: string; isOwner: boolean }> {
  try {
    const user = getUserFromRequest(request);
    if (!user || !user.id) return { hasAccess: false, isOwner: false };

    // User can always access their own dashboard with full permissions
    if (user.id === dashboardOwnerId) {
      return { hasAccess: true, permissionLevel: 'DELETE', isOwner: true };
    }

    // Check if dashboard is shared with user
    const share = await db.prepare(`
      SELECT permission_level
      FROM dashboard_shares
      WHERE owner_id = ? AND shared_with_id = ? AND status = 'active'
    `).bind(dashboardOwnerId, user.id).first();

    if (share) {
      return {
        hasAccess: true,
        permissionLevel: share.permission_level,
        isOwner: false
      };
    }

    return { hasAccess: false, isOwner: false };
  } catch (error) {
    console.error('Error checking dashboard access:', error);
    return { hasAccess: false, isOwner: false };
  }
}

/**
 * Get dashboard context for current user
 */
export async function getDashboardContext(
  request: Request,
  db: D1Database,
  requestedOwnerId?: string
): Promise<{ owner_id: string; owner_name: string | null; owner_email: string; is_own_dashboard: boolean; permission_level?: string } | null> {
  try {
    const user = getUserFromRequest(request);
    if (!user || !user.id) return null;

    // If no specific dashboard requested, default to user's own dashboard
    const ownerId = requestedOwnerId || user.id;

    // Get owner information
    const owner = await db.prepare(
      'SELECT id, email, name FROM users WHERE id = ?'
    ).bind(ownerId).first();

    if (!owner) return null;

    // Check access permissions
    const access = await canAccessDashboard(request, db, ownerId);
    if (!access.hasAccess) return null;

    return {
      owner_id: ownerId,
      owner_name: owner.name,
      owner_email: owner.email,
      is_own_dashboard: access.isOwner,
      permission_level: access.permissionLevel
    };
  } catch (error) {
    console.error('Error getting dashboard context:', error);
    return null;
  }
}

/**
 * Check if user can perform specific action based on permission level
 */
export function canPerformAction(
  userPermission: string,
  requiredPermission: 'VIEW' | 'EDIT' | 'DELETE'
): boolean {
  const permissionHierarchy = {
    'VIEW': 1,
    'EDIT': 2,
    'DELETE': 3
  };

  const userLevel = permissionHierarchy[userPermission as keyof typeof permissionHierarchy] || 0;
  const requiredLevel = permissionHierarchy[requiredPermission];

  return userLevel >= requiredLevel;
}

/**
 * Dashboard access middleware - ensures user can access dashboard with required permission
 */
export async function requireDashboardAccess(
  request: Request,
  db: D1Database,
  dashboardOwnerId: string,
  requiredPermission: 'VIEW' | 'EDIT' | 'DELETE' = 'VIEW'
): Promise<{ success: boolean; error?: string; context?: any }> {
  const access = await canAccessDashboard(request, db, dashboardOwnerId);

  if (!access.hasAccess) {
    return {
      success: false,
      error: 'Dashboard access denied'
    };
  }

  if (!canPerformAction(access.permissionLevel || 'VIEW', requiredPermission)) {
    return {
      success: false,
      error: `Insufficient permissions. Required: ${requiredPermission}`
    };
  }

  return {
    success: true,
    context: {
      isOwner: access.isOwner,
      permissionLevel: access.permissionLevel
    }
  };
}

/**
 * Get list of dashboards accessible to user
 */
export async function getAccessibleDashboards(
  request: Request,
  db: D1Database
): Promise<Array<{ owner_id: string; owner_name: string | null; owner_email: string; permission_level: string; is_own: boolean }>> {
  try {
    const user = getUserFromRequest(request);
    if (!user || !user.id) return [];

    // Get user's own dashboard
    const ownDashboard = await db.prepare(
      'SELECT id, email, name FROM users WHERE id = ?'
    ).bind(user.id).first();

    const dashboards = [];

    if (ownDashboard) {
      dashboards.push({
        owner_id: ownDashboard.id,
        owner_name: ownDashboard.name,
        owner_email: ownDashboard.email,
        permission_level: 'DELETE',
        is_own: true
      });
    }

    // Get shared dashboards
    const sharedDashboards = await db.prepare(`
      SELECT
        ds.owner_id,
        ds.permission_level,
        u.name as owner_name,
        u.email as owner_email
      FROM dashboard_shares ds
      JOIN users u ON ds.owner_id = u.id
      WHERE ds.shared_with_id = ? AND ds.status = 'active'
      ORDER BY u.name, u.email
    `).bind(user.id).all();

    for (const shared of sharedDashboards.results || []) {
      dashboards.push({
        owner_id: shared.owner_id,
        owner_name: shared.owner_name,
        owner_email: shared.owner_email,
        permission_level: shared.permission_level,
        is_own: false
      });
    }

    return dashboards;
  } catch (error) {
    console.error('Error getting accessible dashboards:', error);
    return [];
  }
}
