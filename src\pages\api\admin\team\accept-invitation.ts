import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';
import { getUserIdFromRequest } from '../../../../lib/auth-utils';
import { getDatabase } from '../../../../lib/database';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Parse request body
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation token is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user ID from request (user must be authenticated)
    const userId = getUserIdFromRequest(request);
    if (!userId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User must be authenticated to accept invitation'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Find the invitation
    const invitation = await db.prepare(`
      SELECT * FROM invitations 
      WHERE token = ? AND status = 'pending' AND expires_at > datetime('now')
    `).bind(token).first();

    if (!invitation) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid or expired invitation token'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user details to verify email matches
    const user = await db.prepare(`
      SELECT email FROM users WHERE id = ?
    `).bind(userId).first();

    if (!user || user.email !== invitation.email) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation email does not match authenticated user'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if user is already a team member
    const existingMember = await db.prepare(`
      SELECT id FROM team_members 
      WHERE admin_id = ? AND member_id = ? AND status = 'active'
    `).bind(invitation.admin_id, userId).first();

    if (existingMember) {
      // Mark invitation as accepted even if already a member
      await db.prepare(`
        UPDATE invitations 
        SET status = 'accepted', updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(invitation.id).run();

      return new Response(JSON.stringify({
        success: false,
        error: 'You are already a member of this team'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create team member relationship
    const teamMemberId = uuidv4();
    await db.prepare(`
      INSERT INTO team_members (id, admin_id, member_id, permission_level, status)
      VALUES (?, ?, ?, ?, 'active')
    `).bind(
      teamMemberId,
      invitation.admin_id,
      userId,
      invitation.permission_level
    ).run();

    // Mark invitation as accepted
    await db.prepare(`
      UPDATE invitations 
      SET status = 'accepted', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(invitation.id).run();

    // Get admin details for response
    const admin = await db.prepare(`
      SELECT name, email FROM users WHERE id = ?
    `).bind(invitation.admin_id).first();

    return new Response(JSON.stringify({
      success: true,
      message: 'Invitation accepted successfully',
      data: {
        admin_name: admin?.name || admin?.email,
        permission_level: invitation.permission_level
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error accepting invitation:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
