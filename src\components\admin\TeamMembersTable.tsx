import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Shield, 
  User,
  Calendar
} from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { PermissionSelector } from './PermissionSelector';
import type { TeamMember, PermissionLevel } from '../../types/dashboard';

interface TeamMembersTableProps {
  members: TeamMember[];
  onMemberUpdated: () => void;
  onMemberRemoved: () => void;
}

export const TeamMembersTable: React.FC<TeamMembersTableProps> = ({
  members,
  onMemberUpdated,
  onMemberRemoved,
}) => {
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [memberToRemove, setMemberToRemove] = useState<TeamMember | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const { toast } = useToast();

  const getPermissionBadgeVariant = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return 'secondary';
      case 'EDIT':
        return 'default';
      case 'DELETE':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPermissionIcon = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return '👁️';
      case 'EDIT':
        return '✏️';
      case 'DELETE':
        return '🗑️';
      default:
        return '👁️';
    }
  };

  const handleUpdatePermissions = async (memberId: string, newPermission: PermissionLevel) => {
    setIsUpdating(true);
    
    try {
      const response = await fetch(`/api/admin/team/members/${memberId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          permission_level: newPermission,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Permissions Updated",
          description: "Team member permissions updated successfully",
        });
        onMemberUpdated();
        setEditingMember(null);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update permissions",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast({
        title: "Error",
        description: "Failed to update permissions",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!memberToRemove) return;
    
    setIsRemoving(true);
    
    try {
      const response = await fetch(`/api/admin/team/members/${memberToRemove.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Member Removed",
          description: "Team member removed successfully",
        });
        onMemberRemoved();
        setMemberToRemove(null);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to remove team member",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error removing member:', error);
      toast({
        title: "Error",
        description: "Failed to remove team member",
        variant: "destructive",
      });
    } finally {
      setIsRemoving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (members.length === 0) {
    return (
      <div className="text-center py-12">
        <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-muted-foreground">No Team Members</h3>
        <p className="text-muted-foreground">Invite team members to get started.</p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Member</TableHead>
              <TableHead>Permission Level</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date Added</TableHead>
              <TableHead className="w-[50px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {member.name?.charAt(0) || member.email.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="font-medium">{member.name || 'Unknown'}</div>
                      <div className="text-sm text-muted-foreground">{member.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getPermissionBadgeVariant(member.permission_level)}>
                    <span className="mr-1">{getPermissionIcon(member.permission_level)}</span>
                    {member.permission_level}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>
                    {member.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(member.created_at)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setEditingMember(member)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Permissions
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => setMemberToRemove(member)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remove Access
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Edit Permissions Dialog */}
      {editingMember && (
        <PermissionSelector
          member={editingMember}
          onUpdate={handleUpdatePermissions}
          onCancel={() => setEditingMember(null)}
          isLoading={isUpdating}
        />
      )}

      {/* Remove Member Confirmation Dialog */}
      <AlertDialog open={!!memberToRemove} onOpenChange={() => setMemberToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove <strong>{memberToRemove?.name || memberToRemove?.email}</strong> from your team? 
              This action cannot be undone and they will lose access to all QR codes and analytics.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveMember}
              disabled={isRemoving}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isRemoving ? 'Removing...' : 'Remove Member'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
