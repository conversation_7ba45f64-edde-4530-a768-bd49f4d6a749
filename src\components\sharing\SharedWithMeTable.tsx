import React from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Eye, 
  Calendar,
  ExternalLink
} from 'lucide-react';
import type { SharedDashboard, PermissionLevel } from '../../types/dashboard';

interface SharedWithMeTableProps {
  dashboards: SharedDashboard[];
}

export const SharedWithMeTable: React.FC<SharedWithMeTableProps> = ({
  dashboards,
}) => {
  const getPermissionBadgeVariant = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return 'secondary';
      case 'EDIT':
        return 'default';
      case 'DELETE':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPermissionIcon = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return '👁️';
      case 'EDIT':
        return '✏️';
      case 'DELETE':
        return '🗑️';
      default:
        return '👁️';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleViewDashboard = (ownerId: string) => {
    // Switch to the shared dashboard context
    window.location.href = `/dashboard?owner=${ownerId}`;
  };

  if (dashboards.length === 0) {
    return (
      <div className="text-center py-12">
        <Eye className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-muted-foreground">No Shared Dashboards</h3>
        <p className="text-muted-foreground">No one has shared their dashboard with you yet.</p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Dashboard Owner</TableHead>
            <TableHead>Your Permission Level</TableHead>
            <TableHead>Shared On</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {dashboards.map((dashboard) => (
            <TableRow key={dashboard.owner_id}>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {dashboard.owner_name?.charAt(0) || dashboard.owner_email.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <div className="font-medium">{dashboard.owner_name || 'Unknown'}</div>
                    <div className="text-sm text-muted-foreground">{dashboard.owner_email}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={getPermissionBadgeVariant(dashboard.permission_level)}>
                  <span className="mr-1">{getPermissionIcon(dashboard.permission_level)}</span>
                  {dashboard.permission_level}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(dashboard.shared_at)}</span>
                </div>
              </TableCell>
              <TableCell>
                <Button
                  size="sm"
                  onClick={() => handleViewDashboard(dashboard.owner_id)}
                  className="flex items-center space-x-1"
                >
                  <ExternalLink className="h-4 w-4" />
                  <span>View</span>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
