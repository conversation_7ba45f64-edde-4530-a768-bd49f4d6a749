import type { APIRoute } from 'astro';
import { requireAdmin, getUserIdFromRequest } from '../../../../lib/auth-utils';
import { getDatabase } from '../../../../lib/database';
import type { TeamMembersResponse } from '../../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Check admin privileges
    const adminCheck = await requireAdmin(request, db);
    if (!adminCheck.success) {
      const response: TeamMembersResponse = {
        success: false,
        error: adminCheck.error || 'Admin privileges required'
      };
      return new Response(JSON.stringify(response), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get admin user ID
    const adminId = getUserIdFromRequest(request);
    if (!adminId) {
      const response: TeamMembersResponse = {
        success: false,
        error: 'Admin user not found'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get query parameters for pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const offset = (page - 1) * limit;

    // Build search condition
    let searchCondition = '';
    let searchParams = [adminId];
    
    if (search) {
      searchCondition = 'AND (u.name LIKE ? OR u.email LIKE ?)';
      searchParams.push(`%${search}%`, `%${search}%`);
    }

    // Get team members with user details
    const membersQuery = `
      SELECT 
        tm.id,
        tm.admin_id,
        tm.member_id,
        u.email,
        u.name,
        u.picture,
        tm.permission_level,
        tm.status,
        tm.created_at,
        tm.updated_at
      FROM team_members tm
      JOIN users u ON tm.member_id = u.id
      WHERE tm.admin_id = ? ${searchCondition}
      ORDER BY tm.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const members = await db.prepare(membersQuery)
      .bind(...searchParams, limit, offset)
      .all();

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM team_members tm
      JOIN users u ON tm.member_id = u.id
      WHERE tm.admin_id = ? ${searchCondition}
    `;

    const countResult = await db.prepare(countQuery)
      .bind(...searchParams)
      .first();

    const total = countResult?.total || 0;

    const response: TeamMembersResponse = {
      success: true,
      data: {
        members: members.results || [],
        total
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching team members:', error);
    const response: TeamMembersResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
