import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Shield, 
  User,
  Calendar
} from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { SharePermissionSelector } from './SharePermissionSelector';
import type { DashboardShare, PermissionLevel } from '../../types/dashboard';

interface MySharesTableProps {
  shares: DashboardShare[];
  onShareUpdated: () => void;
  onShareRevoked: () => void;
}

export const MySharesTable: React.FC<MySharesTableProps> = ({
  shares,
  onShareUpdated,
  onShareRevoked,
}) => {
  const [editingShare, setEditingShare] = useState<DashboardShare | null>(null);
  const [shareToRevoke, setShareToRevoke] = useState<DashboardShare | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRevoking, setIsRevoking] = useState(false);
  const { toast } = useToast();

  const getPermissionBadgeVariant = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return 'secondary';
      case 'EDIT':
        return 'default';
      case 'DELETE':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPermissionIcon = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return '👁️';
      case 'EDIT':
        return '✏️';
      case 'DELETE':
        return '🗑️';
      default:
        return '👁️';
    }
  };

  const handleUpdatePermissions = async (shareId: string, newPermission: PermissionLevel) => {
    setIsUpdating(true);
    
    try {
      const response = await fetch(`/api/sharing/shares/${shareId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          permission_level: newPermission,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Permissions Updated",
          description: "Share permissions updated successfully",
        });
        onShareUpdated();
        setEditingShare(null);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update permissions",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast({
        title: "Error",
        description: "Failed to update permissions",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRevokeShare = async () => {
    if (!shareToRevoke) return;
    
    setIsRevoking(true);
    
    try {
      const response = await fetch(`/api/sharing/shares/${shareToRevoke.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Access Revoked",
          description: "Dashboard access revoked successfully",
        });
        onShareRevoked();
        setShareToRevoke(null);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to revoke access",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error revoking access:', error);
      toast({
        title: "Error",
        description: "Failed to revoke access",
        variant: "destructive",
      });
    } finally {
      setIsRevoking(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (shares.length === 0) {
    return (
      <div className="text-center py-12">
        <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-muted-foreground">No Dashboard Shares</h3>
        <p className="text-muted-foreground">Share your dashboard with others to get started.</p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Person</TableHead>
              <TableHead>Permission Level</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Shared On</TableHead>
              <TableHead className="w-[50px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {shares.map((share) => (
              <TableRow key={share.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {share.shared_with_name?.charAt(0) || share.shared_with_email.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="font-medium">{share.shared_with_name || 'Unknown'}</div>
                      <div className="text-sm text-muted-foreground">{share.shared_with_email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getPermissionBadgeVariant(share.permission_level)}>
                    <span className="mr-1">{getPermissionIcon(share.permission_level)}</span>
                    {share.permission_level}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={share.status === 'active' ? 'default' : 'secondary'}>
                    {share.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(share.created_at)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setEditingShare(share)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Permissions
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => setShareToRevoke(share)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Revoke Access
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Edit Permissions Dialog */}
      {editingShare && (
        <SharePermissionSelector
          share={editingShare}
          onUpdate={handleUpdatePermissions}
          onCancel={() => setEditingShare(null)}
          isLoading={isUpdating}
        />
      )}

      {/* Revoke Access Confirmation Dialog */}
      <AlertDialog open={!!shareToRevoke} onOpenChange={() => setShareToRevoke(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Revoke Dashboard Access</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to revoke <strong>{shareToRevoke?.shared_with_name || shareToRevoke?.shared_with_email}</strong>'s access to your dashboard? 
              This action cannot be undone and they will lose access to all your QR codes and analytics.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRevokeShare}
              disabled={isRevoking}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isRevoking ? 'Revoking...' : 'Revoke Access'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
