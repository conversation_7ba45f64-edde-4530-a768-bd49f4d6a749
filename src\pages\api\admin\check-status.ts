import type { APIRoute } from 'astro';
import { isAdmin } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Check if user has admin privileges
    const isUserAdmin = await isAdmin(request, db);

    return new Response(JSON.stringify({
      isAdmin: isUserAdmin
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error checking admin status:', error);
    return new Response(JSON.stringify({
      isAdmin: false,
      error: 'Failed to check admin status'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
