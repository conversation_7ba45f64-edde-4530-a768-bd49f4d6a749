import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Shield, Eye, Edit, Trash2 } from 'lucide-react';
import type { DashboardShare, PermissionLevel } from '../../types/dashboard';

interface SharePermissionSelectorProps {
  share: DashboardShare;
  onUpdate: (shareId: string, newPermission: PermissionLevel) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export const SharePermissionSelector: React.FC<SharePermissionSelectorProps> = ({
  share,
  onUpdate,
  onCancel,
  isLoading,
}) => {
  const [selectedPermission, setSelectedPermission] = useState<PermissionLevel>(share.permission_level);

  const handleUpdate = () => {
    onUpdate(share.id, selectedPermission);
  };

  const getPermissionIcon = (level: PermissionLevel) => {
    switch (level) {
      case 'VIEW':
        return <Eye className="h-4 w-4" />;
      case 'EDIT':
        return <Edit className="h-4 w-4" />;
      case 'DELETE':
        return <Trash2 className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Edit Share Permissions</span>
          </DialogTitle>
          <DialogDescription>
            Update permissions for <strong>{share.shared_with_name || share.shared_with_email}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Permission Level</Label>
            <Select 
              value={selectedPermission} 
              onValueChange={(value: PermissionLevel) => setSelectedPermission(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select permission level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="VIEW">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">View Only</span>
                      <span className="text-sm text-muted-foreground">Read-only access</span>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="EDIT">
                  <div className="flex items-center space-x-2">
                    <Edit className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">Edit Access</span>
                      <span className="text-sm text-muted-foreground">Can modify QR codes</span>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="DELETE">
                  <div className="flex items-center space-x-2">
                    <Trash2 className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">Full Access</span>
                      <span className="text-sm text-muted-foreground">Complete control</span>
                    </div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleUpdate} 
            disabled={isLoading || selectedPermission === share.permission_level}
          >
            {isLoading ? 'Updating...' : 'Update Permissions'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
