import type { APIRoute } from 'astro';
import { getUserIdFromRequest } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';
import type { DashboardSharesResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Get current user (dashboard owner)
    const ownerId = getUserIdFromRequest(request);
    if (!ownerId) {
      const response: DashboardSharesResponse = {
        success: false,
        error: 'Authentication required'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get query parameters for pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const offset = (page - 1) * limit;

    // Build search condition
    let searchCondition = '';
    let searchParams = [ownerId];
    
    if (search) {
      searchCondition = 'AND (u.name LIKE ? OR u.email LIKE ?)';
      searchParams.push(`%${search}%`, `%${search}%`);
    }

    // Get dashboard shares (people I've shared my dashboard with)
    const sharesQuery = `
      SELECT 
        ds.id,
        ds.owner_id,
        ds.shared_with_id,
        owner.email as owner_email,
        owner.name as owner_name,
        owner.picture as owner_picture,
        u.email as shared_with_email,
        u.name as shared_with_name,
        u.picture as shared_with_picture,
        ds.permission_level,
        ds.status,
        ds.created_at,
        ds.updated_at
      FROM dashboard_shares ds
      JOIN users u ON ds.shared_with_id = u.id
      JOIN users owner ON ds.owner_id = owner.id
      WHERE ds.owner_id = ? ${searchCondition}
      ORDER BY ds.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const shares = await db.prepare(sharesQuery)
      .bind(...searchParams, limit, offset)
      .all();

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM dashboard_shares ds
      JOIN users u ON ds.shared_with_id = u.id
      WHERE ds.owner_id = ? ${searchCondition}
    `;

    const countResult = await db.prepare(countQuery)
      .bind(...searchParams)
      .first();

    const total = countResult?.total || 0;

    const response: DashboardSharesResponse = {
      success: true,
      data: {
        shares: shares.results || [],
        total
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching dashboard shares:', error);
    const response: DashboardSharesResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
