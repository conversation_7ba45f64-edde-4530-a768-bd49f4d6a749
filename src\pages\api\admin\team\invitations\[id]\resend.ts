import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';
import { requireAdmin, getUserIdFromRequest } from '../../../../../../lib/auth-utils';
import { getDatabase } from '../../../../../../lib/database';

export const prerender = false;

export const POST: APIRoute = async ({ params, request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    const invitationId = params.id;

    if (!invitationId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check admin privileges
    const adminCheck = await requireAdmin(request, db);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error || 'Admin privileges required'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get admin user ID
    const adminId = getUserIdFromRequest(request);
    if (!adminId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Admin user not found'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Find the invitation and verify it belongs to this admin
    const invitation = await db.prepare(`
      SELECT * FROM invitations 
      WHERE id = ? AND admin_id = ?
    `).bind(invitationId, adminId).first();

    if (!invitation) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation not found or access denied'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if invitation is already accepted
    if (invitation.status === 'accepted') {
      return new Response(JSON.stringify({
        success: false,
        error: 'Cannot resend accepted invitation'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate new token and extend expiration
    const newToken = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Update invitation with new token and expiration
    await db.prepare(`
      UPDATE invitations 
      SET token = ?, expires_at = ?, status = 'pending', updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND admin_id = ?
    `).bind(
      newToken,
      expiresAt.toISOString(),
      invitationId,
      adminId
    ).run();

    // Get updated invitation
    const updatedInvitation = await db.prepare(`
      SELECT * FROM invitations WHERE id = ?
    `).bind(invitationId).first();

    if (!updatedInvitation) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to update invitation'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate new invitation URL
    const baseUrl = new URL(request.url).origin;
    const invitationUrl = `${baseUrl}/admin/team/accept-invitation?token=${newToken}`;

    return new Response(JSON.stringify({
      success: true,
      message: 'Invitation resent successfully',
      data: {
        invitation: updatedInvitation,
        invitation_url: invitationUrl
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error resending invitation:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
