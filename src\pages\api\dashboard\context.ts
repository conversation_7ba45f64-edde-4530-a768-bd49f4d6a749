import type { APIRoute } from 'astro';
import { getDashboardContext } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';
import type { DashboardContextResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Get requested dashboard owner from query params
    const url = new URL(request.url);
    const requestedOwnerId = url.searchParams.get('owner_id') || undefined;

    // Get dashboard context
    const context = await getDashboardContext(request, db, requestedOwnerId);

    if (!context) {
      const response: DashboardContextResponse = {
        success: false,
        error: 'Unable to access dashboard or invalid dashboard owner'
      };
      return new Response(JSON.stringify(response), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const response: DashboardContextResponse = {
      success: true,
      data: context
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error getting dashboard context:', error);
    const response: DashboardContextResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Parse request body
    const body = await request.json();
    const { owner_id } = body;

    if (!owner_id) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Dashboard owner ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get dashboard context for the requested owner
    const context = await getDashboardContext(request, db, owner_id);

    if (!context) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Unable to access dashboard or invalid dashboard owner'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // In a real implementation, you might want to store the current dashboard context
    // in the user's session or a cookie. For now, we'll just return the context.
    
    const response: DashboardContextResponse = {
      success: true,
      data: context
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error switching dashboard context:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
