import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';
import { requireAdmin, getUserIdFromRequest } from '../../../../lib/auth-utils';
import { getDatabase } from '../../../../lib/database';
import type { 
  InviteTeamMemberRequest, 
  InviteTeamMemberResponse,
  PermissionLevel 
} from '../../../../types/dashboard';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Check admin privileges
    const adminCheck = await requireAdmin(request, db);
    if (!adminCheck.success) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: adminCheck.error || 'Admin privileges required'
      };
      return new Response(JSON.stringify(response), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get admin user ID
    const adminId = getUserIdFromRequest(request);
    if (!adminId) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: 'Admin user not found'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body: InviteTeamMemberRequest = await request.json();
    const { email, permission_level } = body;

    // Validate input
    if (!email || !permission_level) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: 'Email and permission level are required'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate permission level
    const validPermissions: PermissionLevel[] = ['VIEW', 'EDIT', 'DELETE'];
    if (!validPermissions.includes(permission_level)) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: 'Invalid permission level'
      };
      return new Response(JSON.stringify(response), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if user is already a team member
    const existingMember = await db.prepare(`
      SELECT tm.id 
      FROM team_members tm
      JOIN users u ON tm.member_id = u.id
      WHERE tm.admin_id = ? AND u.email = ? AND tm.status = 'active'
    `).bind(adminId, email).first();

    if (existingMember) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: 'User is already a team member'
      };
      return new Response(JSON.stringify(response), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if there's already a pending invitation
    const existingInvitation = await db.prepare(`
      SELECT id 
      FROM invitations 
      WHERE admin_id = ? AND email = ? AND status = 'pending' AND expires_at > datetime('now')
    `).bind(adminId, email).first();

    if (existingInvitation) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: 'Pending invitation already exists for this email'
      };
      return new Response(JSON.stringify(response), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate invitation token and expiration (7 days from now)
    const invitationId = uuidv4();
    const token = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create invitation
    await db.prepare(`
      INSERT INTO invitations (id, admin_id, email, permission_level, token, expires_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      invitationId,
      adminId,
      email,
      permission_level,
      token,
      expiresAt.toISOString()
    ).run();

    // Get the created invitation
    const invitation = await db.prepare(`
      SELECT * FROM invitations WHERE id = ?
    `).bind(invitationId).first();

    if (!invitation) {
      const response: InviteTeamMemberResponse = {
        success: false,
        error: 'Failed to create invitation'
      };
      return new Response(JSON.stringify(response), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate invitation URL
    const baseUrl = new URL(request.url).origin;
    const invitationUrl = `${baseUrl}/admin/team/accept-invitation?token=${token}`;

    const response: InviteTeamMemberResponse = {
      success: true,
      data: {
        invitation: invitation as any,
        invitation_url: invitationUrl
      }
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error inviting team member:', error);
    const response: InviteTeamMemberResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
