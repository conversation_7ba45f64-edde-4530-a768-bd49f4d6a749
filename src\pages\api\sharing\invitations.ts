import type { APIRoute } from 'astro';
import { getUserIdFromRequest } from '../../../lib/auth-utils';
import { getDatabase } from '../../../lib/database';
import type { ShareInvitationsResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const db = getDatabase(locals.runtime.env);
    
    // Get current user (dashboard owner)
    const ownerId = getUserIdFromRequest(request);
    if (!ownerId) {
      const response: ShareInvitationsResponse = {
        success: false,
        error: 'Authentication required'
      };
      return new Response(JSON.stringify(response), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get query parameters for pagination and filtering
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status') || 'pending';
    const offset = (page - 1) * limit;

    // Get invitations
    const invitationsQuery = `
      SELECT 
        id,
        owner_id,
        email,
        permission_level,
        token,
        status,
        expires_at,
        created_at,
        updated_at
      FROM share_invitations
      WHERE owner_id = ? AND status = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const invitations = await db.prepare(invitationsQuery)
      .bind(ownerId, status, limit, offset)
      .all();

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM share_invitations
      WHERE owner_id = ? AND status = ?
    `;

    const countResult = await db.prepare(countQuery)
      .bind(ownerId, status)
      .first();

    const total = countResult?.total || 0;

    // Update expired invitations
    await db.prepare(`
      UPDATE share_invitations 
      SET status = 'expired' 
      WHERE owner_id = ? AND status = 'pending' AND expires_at < datetime('now')
    `).bind(ownerId).run();

    const response: ShareInvitationsResponse = {
      success: true,
      data: {
        invitations: invitations.results || [],
        total
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching share invitations:', error);
    const response: ShareInvitationsResponse = {
      success: false,
      error: 'Internal server error'
    };
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
